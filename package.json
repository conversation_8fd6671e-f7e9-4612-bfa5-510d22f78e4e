{"name": "inspiratorweb", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"framer-motion": "^12.23.0", "react": "^19.1.0", "react-dom": "^19.1.0", "three": "^0.178.0", "vanta": "^0.5.24"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "vite": "^6.3.5"}}