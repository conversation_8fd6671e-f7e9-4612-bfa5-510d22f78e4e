import { useState } from 'react'
import './UploadModal.css'

const UploadModal = ({ onClose, autores, onSuccess }) => {
  const [formData, setFormData] = useState({
    content: '',
    name: '',
    img_url: '',
    customName: '',
    customImageUrl: ''
  })
  const [useCustomName, setUseCustomName] = useState(false)
  const [useCustomImage, setUseCustomImage] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  // Get available image URLs from autores data
  const availableImages = autores.map(autor => ({
    url: autor.imagen,
    name: autor.nombre
  }))

  // Get available names from autores data
  const availableNames = autores.map(autor => autor.nombre)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    const finalName = useCustomName ? formData.customName : formData.name
    const finalImageUrl = useCustomImage ? formData.customImageUrl : formData.img_url

    if (!formData.content.trim() || !finalName || !finalImageUrl) {
      setError('Please fill in all fields')
      return
    }

    setIsSubmitting(true)
    setError('')

    const submitData = {
      content: formData.content.trim(),
      name: finalName,
      img_url: finalImageUrl
    }

    try {
      const response = await fetch('/api/sentences/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(true)
        if (onSuccess) {
          onSuccess()
        }
        setTimeout(() => {
          onClose()
        }, 2000)
      } else {
        setError(result.error || result.message || 'Failed to upload sentence')
      }
    } catch (err) {
      console.error('Upload error:', err)
      setError('Network error. Please make sure the API server is running on localhost:3000')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  if (success) {
    return (
      <div className="modal-backdrop" onClick={handleBackdropClick}>
        <div className="modal-content success-modal">
          <div className="success-message">
            <div className="success-icon">✓</div>
            <h2>Success!</h2>
            <p>Your sentence has been uploaded successfully.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content">
        <div className="modal-header">
          <h2>Upload New Sentence</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="upload-form">
          <div className="form-group">
            <label htmlFor="content">Sentence:</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              placeholder="Enter your inspirational sentence..."
              rows={4}
              maxLength={1000}
              required
            />
            <div className="char-count">
              {formData.content.length}/1000
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="name">Author Name:</label>
            <div className="input-with-toggle">
              <select
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required={!useCustomName}
                disabled={useCustomName}
              >
                <option value="">Select an author...</option>
                {availableNames.map((name, index) => (
                  <option key={index} value={name}>
                    {name}
                  </option>
                ))}
              </select>
              <button
                type="button"
                className="toggle-custom"
                onClick={() => setUseCustomName(!useCustomName)}
              >
                {useCustomName ? 'Use Existing' : 'Add New'}
              </button>
            </div>
            {useCustomName && (
              <input
                type="text"
                name="customName"
                value={formData.customName}
                onChange={handleInputChange}
                placeholder="Enter custom author name..."
                required
                style={{ marginTop: '10px' }}
              />
            )}
          </div>

          <div className="form-group">
            <label htmlFor="img_url">Author Image:</label>
            <div className="input-with-toggle">
              <select
                id="img_url"
                name="img_url"
                value={formData.img_url}
                onChange={handleInputChange}
                required={!useCustomImage}
                disabled={useCustomImage}
              >
                <option value="">Select an image...</option>
                {availableImages.map((image, index) => (
                  <option key={index} value={image.url}>
                    {image.name} - {image.url.split('/').pop()}
                  </option>
                ))}
              </select>
              <button
                type="button"
                className="toggle-custom"
                onClick={() => setUseCustomImage(!useCustomImage)}
              >
                {useCustomImage ? 'Use Existing' : 'Add New'}
              </button>
            </div>
            {useCustomImage && (
              <input
                type="url"
                name="customImageUrl"
                value={formData.customImageUrl}
                onChange={handleInputChange}
                placeholder="Enter image URL (https://...)..."
                required
                style={{ marginTop: '10px' }}
              />
            )}
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="form-actions">
            <button
              type="button"
              onClick={onClose}
              className="cancel-button"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="submit-button"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Uploading...' : 'Upload Sentence'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default UploadModal
